cmake_minimum_required(VERSION 3.16)
project(daemon_logon_atk_bat)

set(CMAKE_CXX_STANDARD 98)

# OS 및 OpenSSL 버전 감지
execute_process(COMMAND lsb_release -rs OUTPUT_VARIABLE OS_VERSION OUTPUT_STRIP_TRAILING_WHITESPACE ERROR_QUIET)
execute_process(COMMAND cat /etc/redhat-release OUTPUT_VARIABLE REDHAT_RELEASE OUTPUT_STRIP_TRAILING_WHITESPACE ERROR_QUIET)

# OpenSSL 버전 확인
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(OPENSSL openssl)
    if(OPENSSL_FOUND)
        set(OPENSSL_VERSION ${OPENSSL_VERSION})
    endif()
endif()

# OpenSSL 헤더에서 버전 직접 확인
if(NOT OPENSSL_VERSION)
    find_path(OPENSSL_INCLUDE_DIR openssl/opensslv.h)
    if(OPENSSL_INCLUDE_DIR)
        file(STRINGS "${OPENSSL_INCLUDE_DIR}/openssl/opensslv.h" 
             OPENSSL_VERSION_LINE REGEX "^#define OPENSSL_VERSION_NUMBER")
        string(REGEX MATCH "0x[0-9a-fA-F]+" OPENSSL_VERSION_HEX "${OPENSSL_VERSION_LINE}")
        
        # OpenSSL 버전 분류
        if(OPENSSL_VERSION_HEX VERSION_GREATER_EQUAL "0x30000000")
            set(OPENSSL_MAJOR_VERSION 3)
        elseif(OPENSSL_VERSION_HEX VERSION_GREATER_EQUAL "0x10100000")
            set(OPENSSL_MAJOR_VERSION 1.1)
        else()
            set(OPENSSL_MAJOR_VERSION 1.0)
        endif()
    endif()
endif()

# OS별 컴파일 플래그 설정
# 모든 환경에서 OpenSSL 호환성 문제를 해결하기 위해 강제로 레거시 설정 적용
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -Wall -std=gnu++98 -w -Wno-deprecated-declarations")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DOPENSSL_API_COMPAT=0x10100000L -DOPENSSL_SUPPRESS_DEPRECATED")

if(REDHAT_RELEASE MATCHES "CentOS.*6\\.")
    # CentOS 6.x 환경 (OpenSSL 1.0.1)
    message(STATUS "Building for CentOS 6.x with OpenSSL 1.0.1")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DCENTOS_6X")
    set(LEGACY_OPENSSL TRUE)
elseif(REDHAT_RELEASE MATCHES "CentOS.*7\\.")
    # CentOS 7.x 환경 (OpenSSL 1.0.2)
    message(STATUS "Building for CentOS 7.x with OpenSSL 1.0.2")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DCENTOS_7X")
    set(LEGACY_OPENSSL TRUE)
elseif(REDHAT_RELEASE MATCHES "Rocky Linux.*9")
    # Rocky Linux 9 환경 (OpenSSL 3.x)
    message(STATUS "Building for Rocky Linux 9 with OpenSSL 3.x")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DROCKY_LINUX_9")
    set(MODERN_OPENSSL TRUE)
else()
    # 기타 환경 (OpenSSL 버전에 따라 결정)
    if(OPENSSL_MAJOR_VERSION STREQUAL "3")
        message(STATUS "Building with OpenSSL 3.x")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DOPENSSL_3X")
        set(MODERN_OPENSSL TRUE)
    elseif(OPENSSL_MAJOR_VERSION STREQUAL "1.1")
        message(STATUS "Building with OpenSSL 1.1.x")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DOPENSSL_11X")
        set(LEGACY_OPENSSL TRUE)
    else()
        message(STATUS "Building with OpenSSL 1.0.x or older")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DOPENSSL_10X")
        set(LEGACY_OPENSSL TRUE)
    endif()
    # 기본값으로 MODERN_OPENSSL 설정 (OpenSSL 3.x 대응)
    if(NOT DEFINED LEGACY_OPENSSL)
        set(MODERN_OPENSSL TRUE)
    endif()
endif()

# Oracle 환경 설정
set(ORACLE_HOME "/usr/lib/oracle/21/client64")
set(PROC_INCLUDE "/usr/include/oracle/21/client64")
set(PROC_CONFIG "/usr/lib/oracle/21/client64/lib/precomp/admin/pcscfg.cfg")

# Oracle Pro*C 컴파일러 찾기
find_program(PROC_EXECUTABLE proc PATHS ${ORACLE_HOME}/bin)

# Oracle Pro*C 전처리 함수
function(add_proc_source target_name source_file)
    get_filename_component(source_name ${source_file} NAME_WE)

    set(pc_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.pc)
    set(cpp_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.cpp)

    # .cpp를 .pc로 복사
    add_custom_command(
        OUTPUT ${pc_file}
        COMMAND ${CMAKE_COMMAND} -E copy ${source_file} ${pc_file}
        DEPENDS ${source_file}
        COMMENT "Copying ${source_file} to ${pc_file}"
    )

    # 특정 파일들에만 THREADS=YES 사용 (필요에 따라 조건 수정 가능)
    if(source_name MATCHES "logonDB|logonSession")
        set(THREADS_OPTION "THREADS=YES")
    else()
        set(THREADS_OPTION "")
    endif()

    # Oracle Pro*C 전처리
    add_custom_command(
        OUTPUT ${cpp_file}
        COMMAND ${CMAKE_COMMAND} -E env
            "LD_LIBRARY_PATH=${ORACLE_HOME}/lib:$ENV{LD_LIBRARY_PATH}"
            "ORACLE_HOME=${ORACLE_HOME}"
            "TNS_ADMIN=${ORACLE_HOME}/network/admin"
            ${PROC_EXECUTABLE}
            MODE=ORACLE
            DBMS=V7
            UNSAFE_NULL=YES
            CHAR_MAP=STRING
            iname=${pc_file}
            include=${CMAKE_CURRENT_SOURCE_DIR}/inc
            include=${PROC_INCLUDE}
            include=${CMAKE_SOURCE_DIR}/../command_logon_atk_bat/inc
            ${THREADS_OPTION}
            CPP_SUFFIX=cpp
            CODE=CPP
            PARSE=NONE
            CTIMEOUT=3
            define=__sparc
            config=${PROC_CONFIG}
            SQLCHECK=SEMANTICS
            userid=neoatk/neoatk@NEO226
        DEPENDS ${pc_file}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Processing ${pc_file} with Oracle Pro*C"
    )

    target_sources(${target_name} PRIVATE ${cpp_file})
endfunction()

# Include directories
include_directories(
    inc
    lib
    ${CMAKE_SOURCE_DIR}/../command_logon_atk_bat/inc
    ${PROC_INCLUDE}
    $ENV{HOME}/library
)

# 라이브러리 디렉토리
link_directories(
    ${ORACLE_HOME}/lib
    /usr/lib64
    $ENV{HOME}/library
    ${CMAKE_SOURCE_DIR}/../libsrc/orapp
)

# 컴파일 정의
add_definitions(
    -D_GNU_SOURCE
    -D_REENTRANT
)

# Oracle 컴파일 플래그
set(ORACLE_COMPILE_FLAGS 
    "-D_LOGON_MODE"
)

# 공통 라이브러리가 있다면 생성 (lib 디렉토리 확인 필요)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/lib)
    file(GLOB LIB_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/lib/*.cpp")
    # command_util.cpp는 makefile에서 사용하지 않으므로 제외
    list(FILTER LIB_SOURCES EXCLUDE REGEX ".*command_util\\.cpp$")
    # DatabaseORA_MMS.cpp는 Oracle Pro*C 파일이므로 별도 처리
    list(FILTER LIB_SOURCES EXCLUDE REGEX ".*DatabaseORA_MMS\\.cpp$")
    if(LIB_SOURCES)
        add_library(daemon_logon_lib STATIC ${LIB_SOURCES})
    endif()
endif()

# DatabaseORA_MMS Oracle Pro*C 라이브러리 (별도 처리)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/lib/DatabaseORA_MMS.cpp)
    add_library(database_ora_mms STATIC)
    add_proc_source(database_ora_mms ${CMAKE_CURRENT_SOURCE_DIR}/lib/DatabaseORA_MMS.cpp)
    target_compile_options(database_ora_mms PRIVATE ${ORACLE_COMPILE_FLAGS})
endif()

# Database and process related executables
add_executable(logonDB)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/logonDB.cpp)
    # 파일 내용을 읽어서 EXEC SQL이 있는지 확인
    file(READ ${CMAKE_CURRENT_SOURCE_DIR}/src/logonDB.cpp LOGONDB_CONTENT)
    string(FIND "${LOGONDB_CONTENT}" "EXEC SQL" EXEC_SQL_FOUND)
    
    if(EXEC_SQL_FOUND GREATER -1)
        # EXEC SQL이 있으면 Pro*C 처리
        add_proc_source(logonDB ${CMAKE_CURRENT_SOURCE_DIR}/src/logonDB.cpp)
        target_compile_options(logonDB PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        # EXEC SQL이 없으면 일반 C++ 파일로 처리
        target_sources(logonDB PRIVATE src/logonDB.cpp)
    endif()
endif()

add_executable(logonSession)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/logonSession.cpp)
    # 파일 내용을 읽어서 EXEC SQL이 있는지 확인
    file(READ ${CMAKE_CURRENT_SOURCE_DIR}/src/logonSession.cpp LOGONSESSION_CONTENT)
    string(FIND "${LOGONSESSION_CONTENT}" "EXEC SQL" EXEC_SQL_FOUND)
    
    if(EXEC_SQL_FOUND GREATER -1)
        # EXEC SQL이 있으면 Pro*C 처리
        add_proc_source(logonSession ${CMAKE_CURRENT_SOURCE_DIR}/src/logonSession.cpp)
        target_compile_options(logonSession PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        # EXEC SQL이 없으면 일반 C++ 파일로 처리
        target_sources(logonSession PRIVATE src/logonSession.cpp)
    endif()
endif()

add_executable(adminProcess)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/adminProcess.cpp)
    target_sources(adminProcess PRIVATE src/adminProcess.cpp)
endif()

add_executable(monitorProcess)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/monitorProcess.cpp)
    target_sources(monitorProcess PRIVATE src/monitorProcess.cpp)
endif()

# Sender processes
add_executable(senderAtalkCpool)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderAtalkCpool.cpp)
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderAtalkCpool.pc OR 
       PROC_EXECUTABLE AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderAtalkCpool.cpp)
        add_proc_source(senderAtalkCpool ${CMAKE_CURRENT_SOURCE_DIR}/src/senderAtalkCpool.cpp)
        target_compile_options(senderAtalkCpool PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        target_sources(senderAtalkCpool PRIVATE src/senderAtalkCpool.cpp)
    endif()
endif()

add_executable(senderAtalkProDB)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderAtalkProDB.cpp)
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderAtalkProDB.pc OR 
       PROC_EXECUTABLE AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderAtalkProDB.cpp)
        add_proc_source(senderAtalkProDB ${CMAKE_CURRENT_SOURCE_DIR}/src/senderAtalkProDB.cpp)
        target_compile_options(senderAtalkProDB PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        target_sources(senderAtalkProDB PRIVATE src/senderAtalkProDB.cpp)
    endif()
endif()

add_executable(senderMMSProcess)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderMMSProcess.cpp)
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderMMSProcess.pc OR 
       PROC_EXECUTABLE AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderMMSProcess.cpp)
        add_proc_source(senderMMSProcess ${CMAKE_CURRENT_SOURCE_DIR}/src/senderMMSProcess.cpp)
        target_compile_options(senderMMSProcess PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        target_sources(senderMMSProcess PRIVATE src/senderMMSProcess.cpp)
    endif()
endif()

# Report processes
add_executable(reportMMSProcess)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcess.cpp)
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcess.pc OR 
       PROC_EXECUTABLE AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcess.cpp)
        add_proc_source(reportMMSProcess ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcess.cpp)
        target_compile_options(reportMMSProcess PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        target_sources(reportMMSProcess PRIVATE src/reportMMSProcess.cpp)
    endif()
endif()

# Admin process (adminUtil.cpp 포함)
add_executable(admin)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/admin.cpp)
    target_sources(admin PRIVATE src/admin.cpp)
endif()
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/lib/adminUtil.cpp)
    target_sources(admin PRIVATE lib/adminUtil.cpp)
endif()

# MMS DB processes
add_executable(senderMMSDB)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderMMSDB.cpp)
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderMMSDB.pc OR 
       PROC_EXECUTABLE AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderMMSDB.cpp)
        add_proc_source(senderMMSDB ${CMAKE_CURRENT_SOURCE_DIR}/src/senderMMSDB.cpp)
        target_compile_options(senderMMSDB PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        target_sources(senderMMSDB PRIVATE src/senderMMSDB.cpp)
    endif()
endif()

add_executable(reportMMSDB)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSDB.cpp)
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSDB.pc OR 
       PROC_EXECUTABLE AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSDB.cpp)
        add_proc_source(reportMMSDB ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSDB.cpp)
        target_compile_options(reportMMSDB PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        target_sources(reportMMSDB PRIVATE src/reportMMSDB.cpp)
    endif()
endif()

add_executable(reportMMSProcDB)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcessDB.cpp)
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcessDB.pc OR 
       PROC_EXECUTABLE AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcessDB.cpp)
        add_proc_source(reportMMSProcDB ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcessDB.cpp)
        target_compile_options(reportMMSProcDB PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        target_sources(reportMMSProcDB PRIVATE src/reportMMSProcessDB.cpp)
    endif()
endif()

add_executable(senderAtalkCryDB)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderAtalkCryDB.cpp)
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderAtalkCryDB.pc OR 
       PROC_EXECUTABLE AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderAtalkCryDB.cpp)
        add_proc_source(senderAtalkCryDB ${CMAKE_CURRENT_SOURCE_DIR}/src/senderAtalkCryDB.cpp)
        target_compile_options(senderAtalkCryDB PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        target_sources(senderAtalkCryDB PRIVATE src/senderAtalkCryDB.cpp)
    endif()
endif()

# 링크 라이브러리 설정
set(TARGETS logonSession logonDB admin senderMMSDB reportMMSDB
            monitorProcess adminProcess senderMMSProcess reportMMSProcess 
            reportMMSProcDB senderAtalkProDB senderAtalkCpool senderAtalkCryDB)

foreach(target ${TARGETS})
    target_include_directories(${target} PRIVATE inc)
    
    # 외부 라이브러리 빌드 의존성 추가
    add_dependencies(${target} build_all_libs)
    
    # 공통 라이브러리가 있다면 링크
    if(TARGET daemon_logon_lib)
        target_link_libraries(${target} daemon_logon_lib)
        add_dependencies(daemon_logon_lib build_all_libs)
    endif()
    
    # DatabaseORA_MMS Oracle Pro*C 라이브러리 링크
    if(TARGET database_ora_mms)
        target_link_libraries(${target} database_ora_mms)
    endif()
    
    # command_logon_atk_bat의 오브젝트 파일이 있다면 링크 (admin 제외)
    if(EXISTS ${CMAKE_SOURCE_DIR}/../command_logon_atk_bat/obj/sms_ctrlsub++.o AND NOT target STREQUAL "admin")
        target_link_libraries(${target} ${CMAKE_SOURCE_DIR}/../command_logon_atk_bat/obj/sms_ctrlsub++.o)
    endif()
    
    # libkskyb 라이브러리들 링크 (makefile에서 사용하는 라이브러리들)
    target_link_libraries(${target} 
        ksbase64
        kssocket
        ksconfig
        ksthread
    )
    
    # orapp 라이브러리 링크
    target_link_libraries(${target} orapp)
    
    # Oracle 관련 실행파일에는 Oracle 라이브러리 링크
    if(target MATCHES "logonDB|logonSession|sender.*|report.*")
        target_link_libraries(${target} clntsh)
    endif()
    
    # OpenSSL crypto 라이브러리 링크 (Encrypt 클래스 사용)
    # 모든 환경에서 OpenSSL 3.x 호환성 문제를 해결하기 위해 강제로 레거시 설정 적용
    target_link_libraries(${target} crypto ssl)
    
    # OpenSSL 3.x에서 레거시 함수 사용을 위한 강력한 컴파일 플래그 추가
    target_compile_definitions(${target} PRIVATE 
        OPENSSL_API_COMPAT=0x10100000L
        OPENSSL_SUPPRESS_DEPRECATED
        OPENSSL_NO_DEPRECATED_3_0
    )
    
    # 레거시 provider 활성화를 위한 추가 라이브러리 시도
    find_library(OPENSSL_LEGACY_LIB legacy PATHS /usr/lib64 /usr/lib /usr/lib/x86_64-linux-gnu)
    if(OPENSSL_LEGACY_LIB)
        target_link_libraries(${target} ${OPENSSL_LEGACY_LIB})
    endif()
    
    # OpenSSL 1.1 정적 라이브러리 시도
    find_library(OPENSSL_CRYPTO_11 crypto-1.1 PATHS /usr/lib64 /usr/lib /usr/lib/x86_64-linux-gnu)
    if(OPENSSL_CRYPTO_11)
        target_link_libraries(${target} ${OPENSSL_CRYPTO_11})
    endif()
    
    # 시스템 라이브러리 링크
    target_link_libraries(${target} 
        pthread
        dl
    )
endforeach()

# Set output directories
set_target_properties(${TARGETS}
                      PROPERTIES
                      RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/bin"
)

# Add custom target for Makefile build
add_custom_target(makefile_build_daemon_logon
    COMMAND make -C ${CMAKE_CURRENT_SOURCE_DIR}/mak
    COMMENT "Building daemon logon with original Makefile"
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

# Add custom target for Makefile clean
add_custom_target(makefile_clean_daemon_logon
    COMMAND make -C ${CMAKE_CURRENT_SOURCE_DIR}/mak clean
    COMMENT "Cleaning daemon logon with original Makefile"
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

# libkskyb 라이브러리 빌드 설정
add_custom_target(build_libkskyb
    COMMAND ${CMAKE_COMMAND} -S ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb -B ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb/cmake-build
    COMMAND ${CMAKE_COMMAND} --build ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb/cmake-build
    COMMAND ${CMAKE_COMMAND} --build ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb/cmake-build --target install_libkskyb
    COMMENT "Building and installing libkskyb libraries to $HOME/library"
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb
)

# orapp 라이브러리 빌드 설정
add_custom_target(build_orapp
    COMMAND ${CMAKE_COMMAND} -S ${CMAKE_SOURCE_DIR}/../libsrc/orapp -B ${CMAKE_SOURCE_DIR}/../libsrc/orapp/cmake-build
    COMMAND ${CMAKE_COMMAND} --build ${CMAKE_SOURCE_DIR}/../libsrc/orapp/cmake-build
    COMMAND ${CMAKE_COMMAND} --build ${CMAKE_SOURCE_DIR}/../libsrc/orapp/cmake-build --target install_orapp
    COMMENT "Building and installing orapp library to $HOME/library"
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}/../libsrc/orapp
)

# 모든 외부 라이브러리 빌드
add_custom_target(build_all_libs
    DEPENDS build_libkskyb build_orapp
    COMMENT "Building all external libraries"
)
