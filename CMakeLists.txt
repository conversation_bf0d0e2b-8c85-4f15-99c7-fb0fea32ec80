cmake_minimum_required(VERSION 3.16)
project(neoatk_bat_216)

set(CMAKE_CXX_STANDARD 98)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Oracle 환경 설정
set(ORACLE_HOME "/usr/lib/oracle/21/client64")
set(PROC_INCLUDE "/usr/include/oracle/21/client64")

# Oracle Pro*C 컴파일러 설정
find_program(PROC_EXECUTABLE proc PATHS ${ORACLE_HOME}/bin)
if(NOT PROC_EXECUTABLE)
    message(FATAL_ERROR "Oracle Pro*C compiler not found")
endif()

# 서브디렉토리 추가
add_subdirectory(command_alimtalk_bat)
add_subdirectory(daemon_alimtalk_bat)
add_subdirectory(command_logon_atk_bat)
add_subdirectory(daemon_logon_atk_bat)

# Optional: Set global properties for IDE organization
set_property(GLOBAL PROPERTY USE_FOLDERS ON)

# Group projects in IDE folders
if(TARGET cmd_alim)
    set_target_properties(cmd_alim mnt_alim log_alim dog_alim crt_alim PROPERTIES FOLDER "AlimTalk Command")
endif()

if(TARGET cmd_logon)
    set_target_properties(cmd_logon mnt_logon log_logon dog_logon crt_logon PROPERTIES FOLDER "Logon Command")
endif()

if(TARGET atalk_send_v3)
    set_target_properties(atalk_send_v3 atalk_send_v4 PollingReports PROPERTIES FOLDER "AlimTalk Daemon")
endif()

if(TARGET logonDB)
    set_target_properties(logonDB logonSession adminProcess monitorProcess PROPERTIES FOLDER "Logon Daemon/Core")
endif()

if(TARGET senderAtalkCpool)
    set_target_properties(senderAtalkCpool senderAtalkProDB senderMMSProcess PROPERTIES FOLDER "Logon Daemon/Senders")
endif()

if(TARGET reportMMSProcess)
    set_target_properties(reportMMSProcess alertCID alertRescode PROPERTIES FOLDER "Logon Daemon/Reports")
endif()
